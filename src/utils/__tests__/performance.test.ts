/**
 * Tests for performance utility functions
 * 
 * Comprehensive test suite covering metric recording, reporting, and utility functions
 */

import React from 'react';
import { renderHook, act } from '@testing-library/react';
import {
  PERFORMANCE_THRESHOLDS,
  performanceMonitor,
  withPerformanceMonitoring,
  performanceMonitored,
  usePerformanceMonitoring,
  measurePerformance,
  startMemoryMonitoring,
  usePerformanceReport,
  PerformanceMetric,
  MemoryMetric,
  PerformanceReport,
} from '../performance';

// Mock console methods
const originalConsole = { ...console };
beforeEach(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  performanceMonitor.clearMetrics();
  performanceMonitor.setEnabled(true);
});

afterEach(() => {
  Object.assign(console, originalConsole);
});

// Mock performance.now
const mockPerformanceNow = jest.fn();
Object.defineProperty(global.performance, 'now', {
  value: mockPerformanceNow,
  writable: true,
});

// Mock performance.memory
Object.defineProperty(global.performance, 'memory', {
  value: {
    usedJSHeapSize: 10 * 1024 * 1024, // 10MB
    totalJSHeapSize: 50 * 1024 * 1024, // 50MB
    jsHeapSizeLimit: 100 * 1024 * 1024, // 100MB
  },
  writable: true,
});

describe('performance utilities', () => {
  describe('PERFORMANCE_THRESHOLDS', () => {
    it('should have correct threshold values', () => {
      expect(PERFORMANCE_THRESHOLDS.SLOW_OPERATION).toBe(100);
      expect(PERFORMANCE_THRESHOLDS.VERY_SLOW_OPERATION).toBe(500);
      expect(PERFORMANCE_THRESHOLDS.CRITICAL_OPERATION).toBe(1000);
      expect(PERFORMANCE_THRESHOLDS.MEMORY_WARNING).toBe(50 * 1024 * 1024);
      expect(PERFORMANCE_THRESHOLDS.MEMORY_CRITICAL).toBe(100 * 1024 * 1024);
    });
  });

  describe('PerformanceMonitor', () => {
    describe('setEnabled', () => {
      it('should enable/disable monitoring', () => {
        performanceMonitor.setEnabled(false);
        expect(console.log).toHaveBeenCalledWith('Performance monitoring disabled');

        performanceMonitor.setEnabled(true);
        expect(console.log).toHaveBeenCalledWith('Performance monitoring enabled');
      });
    });

    describe('recordMetric', () => {
      beforeEach(() => {
        mockPerformanceNow.mockReturnValue(1000);
        jest.spyOn(Date, 'now').mockReturnValue(1234567890);
      });

      afterEach(() => {
        jest.restoreAllMocks();
      });

      it('should record normal operation metrics', () => {
        performanceMonitor.recordMetric({
          operation: 'test-operation',
          duration: 50,
          category: 'computation',
        });

        const report = performanceMonitor.getReport();
        expect(report.totalOperations).toBe(1);
        expect(report.slowOperations).toHaveLength(0);
        expect(report.criticalOperations).toHaveLength(0);
      });

      it('should record slow operation metrics', () => {
        performanceMonitor.recordMetric({
          operation: 'slow-operation',
          duration: 150,
          category: 'computation',
        });

        const report = performanceMonitor.getReport();
        expect(report.slowOperations).toHaveLength(1);
        expect(report.slowOperations[0].severity).toBe('slow');
        expect(console.warn).toHaveBeenCalledWith(
          expect.stringContaining('🐌 Slow operation detected'),
          expect.any(Object)
        );
      });

      it('should record very slow operation metrics', () => {
        performanceMonitor.recordMetric({
          operation: 'very-slow-operation',
          duration: 600,
          category: 'computation',
        });

        const report = performanceMonitor.getReport();
        expect(report.slowOperations).toHaveLength(1);
        expect(report.slowOperations[0].severity).toBe('very_slow');
      });

      it('should record critical operation metrics', () => {
        performanceMonitor.recordMetric({
          operation: 'critical-operation',
          duration: 1200,
          category: 'computation',
        });

        const report = performanceMonitor.getReport();
        expect(report.criticalOperations).toHaveLength(1);
        expect(report.criticalOperations[0].severity).toBe('critical');
      });

      it('should not record metrics when disabled', () => {
        performanceMonitor.setEnabled(false);
        performanceMonitor.recordMetric({
          operation: 'test-operation',
          duration: 50,
          category: 'computation',
        });

        const report = performanceMonitor.getReport();
        expect(report.totalOperations).toBe(0);
      });

      it('should include metadata in metrics', () => {
        const metadata = { userId: '123', feature: 'timer' };
        performanceMonitor.recordMetric({
          operation: 'test-operation',
          duration: 50,
          category: 'computation',
          metadata,
        });

        const metrics = performanceMonitor.getMetricsByOperation('test-operation');
        expect(metrics[0].metadata).toEqual(metadata);
      });

      it('should limit the number of stored metrics', () => {
        // Record more than maxMetrics (1000)
        for (let i = 0; i < 1100; i++) {
          performanceMonitor.recordMetric({
            operation: `operation-${i}`,
            duration: 50,
            category: 'computation',
          });
        }

        const report = performanceMonitor.getReport();
        expect(report.totalOperations).toBe(1000);
      });
    });

    describe('recordMemoryUsage', () => {
      it('should record memory usage when available', () => {
        performanceMonitor.recordMemoryUsage();

        const report = performanceMonitor.getReport();
        expect(report.memoryUsage).toHaveLength(1);
        expect(report.memoryUsage[0]).toMatchObject({
          usedJSHeapSize: 10 * 1024 * 1024,
          totalJSHeapSize: 50 * 1024 * 1024,
          jsHeapSizeLimit: 100 * 1024 * 1024,
          percentage: 10,
        });
      });

      it('should warn about high memory usage', () => {
        // Mock high memory usage
        Object.defineProperty(global.performance, 'memory', {
          value: {
            usedJSHeapSize: 60 * 1024 * 1024, // 60MB (above warning threshold)
            totalJSHeapSize: 80 * 1024 * 1024,
            jsHeapSizeLimit: 100 * 1024 * 1024,
          },
          writable: true,
        });

        performanceMonitor.recordMemoryUsage();

        expect(console.warn).toHaveBeenCalledWith(
          expect.stringContaining('🧠 WARNING: High memory usage detected'),
          expect.any(Object)
        );
      });

      it('should warn about critical memory usage', () => {
        // Mock critical memory usage
        Object.defineProperty(global.performance, 'memory', {
          value: {
            usedJSHeapSize: 110 * 1024 * 1024, // 110MB (above critical threshold)
            totalJSHeapSize: 120 * 1024 * 1024,
            jsHeapSizeLimit: 150 * 1024 * 1024,
          },
          writable: true,
        });

        performanceMonitor.recordMemoryUsage();

        expect(console.warn).toHaveBeenCalledWith(
          expect.stringContaining('🧠 CRITICAL: High memory usage detected'),
          expect.any(Object)
        );
      });

      it('should not record memory when disabled', () => {
        performanceMonitor.setEnabled(false);
        performanceMonitor.recordMemoryUsage();

        const report = performanceMonitor.getReport();
        expect(report.memoryUsage).toHaveLength(0);
      });

      it('should not record memory when performance.memory is unavailable', () => {
        delete (global.performance as any).memory;
        performanceMonitor.recordMemoryUsage();

        const report = performanceMonitor.getReport();
        expect(report.memoryUsage).toHaveLength(0);
      });

      it('should limit the number of stored memory metrics', () => {
        // Record more than maxMemoryMetrics (100)
        for (let i = 0; i < 110; i++) {
          performanceMonitor.recordMemoryUsage();
        }

        const report = performanceMonitor.getReport();
        expect(report.memoryUsage).toHaveLength(10); // Only last 10 in report
      });
    });

    describe('getReport', () => {
      beforeEach(() => {
        // Add some test metrics
        performanceMonitor.recordMetric({
          operation: 'fast-op',
          duration: 50,
          category: 'computation',
        });
        performanceMonitor.recordMetric({
          operation: 'slow-op',
          duration: 150,
          category: 'timer',
        });
        performanceMonitor.recordMetric({
          operation: 'critical-op',
          duration: 1200,
          category: 'storage',
        });
      });

      it('should generate comprehensive performance report', () => {
        const report = performanceMonitor.getReport();

        expect(report.totalOperations).toBe(3);
        expect(report.averageDuration).toBeCloseTo((50 + 150 + 1200) / 3, 2);
        expect(report.slowOperations).toHaveLength(1);
        expect(report.criticalOperations).toHaveLength(1);
        expect(report.recommendations).toBeInstanceOf(Array);
        expect(report.recommendations.length).toBeGreaterThan(0);
      });

      it('should handle empty metrics', () => {
        performanceMonitor.clearMetrics();
        const report = performanceMonitor.getReport();

        expect(report.totalOperations).toBe(0);
        expect(report.averageDuration).toBe(0);
        expect(report.slowOperations).toHaveLength(0);
        expect(report.criticalOperations).toHaveLength(0);
      });
    });

    describe('clearMetrics', () => {
      it('should clear all metrics and memory data', () => {
        performanceMonitor.recordMetric({
          operation: 'test-operation',
          duration: 50,
          category: 'computation',
        });
        performanceMonitor.recordMemoryUsage();

        performanceMonitor.clearMetrics();

        const report = performanceMonitor.getReport();
        expect(report.totalOperations).toBe(0);
        expect(report.memoryUsage).toHaveLength(0);
        expect(console.log).toHaveBeenCalledWith('Performance metrics cleared');
      });
    });

    describe('getMetricsByCategory', () => {
      beforeEach(() => {
        performanceMonitor.recordMetric({
          operation: 'timer-op',
          duration: 50,
          category: 'timer',
        });
        performanceMonitor.recordMetric({
          operation: 'storage-op',
          duration: 100,
          category: 'storage',
        });
        performanceMonitor.recordMetric({
          operation: 'ui-op',
          duration: 75,
          category: 'ui',
        });
      });

      it('should filter metrics by category', () => {
        const timerMetrics = performanceMonitor.getMetricsByCategory('timer');
        const storageMetrics = performanceMonitor.getMetricsByCategory('storage');
        const uiMetrics = performanceMonitor.getMetricsByCategory('ui');

        expect(timerMetrics).toHaveLength(1);
        expect(timerMetrics[0].operation).toBe('timer-op');
        expect(storageMetrics).toHaveLength(1);
        expect(storageMetrics[0].operation).toBe('storage-op');
        expect(uiMetrics).toHaveLength(1);
        expect(uiMetrics[0].operation).toBe('ui-op');
      });

      it('should return empty array for non-existent category', () => {
        const networkMetrics = performanceMonitor.getMetricsByCategory('network');
        expect(networkMetrics).toHaveLength(0);
      });
    });

    describe('getMetricsByOperation', () => {
      beforeEach(() => {
        performanceMonitor.recordMetric({
          operation: 'test-operation',
          duration: 50,
          category: 'computation',
        });
        performanceMonitor.recordMetric({
          operation: 'test-operation',
          duration: 100,
          category: 'computation',
        });
        performanceMonitor.recordMetric({
          operation: 'other-operation',
          duration: 75,
          category: 'computation',
        });
      });

      it('should filter metrics by operation name', () => {
        const testMetrics = performanceMonitor.getMetricsByOperation('test-operation');
        const otherMetrics = performanceMonitor.getMetricsByOperation('other-operation');

        expect(testMetrics).toHaveLength(2);
        expect(otherMetrics).toHaveLength(1);
        expect(otherMetrics[0].operation).toBe('other-operation');
      });

      it('should return empty array for non-existent operation', () => {
        const nonExistentMetrics = performanceMonitor.getMetricsByOperation('non-existent');
        expect(nonExistentMetrics).toHaveLength(0);
      });
    });
  });

  describe('withPerformanceMonitoring', () => {
    beforeEach(() => {
      mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1150);
    });

    it('should wrap synchronous functions', () => {
      const originalFn = jest.fn((x: number, y: number) => x + y);
      const wrappedFn = withPerformanceMonitoring(originalFn, 'add-operation', 'computation');

      const result = wrappedFn(2, 3);

      expect(result).toBe(5);
      expect(originalFn).toHaveBeenCalledWith(2, 3);

      const metrics = performanceMonitor.getMetricsByOperation('add-operation');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].duration).toBe(150);
      expect(metrics[0].category).toBe('computation');
    });

    it('should wrap asynchronous functions', async () => {
      const originalFn = jest.fn(async (x: number) => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return x * 2;
      });
      const wrappedFn = withPerformanceMonitoring(originalFn, 'async-operation', 'network');

      const result = await wrappedFn(5);

      expect(result).toBe(10);
      expect(originalFn).toHaveBeenCalledWith(5);

      const metrics = performanceMonitor.getMetricsByOperation('async-operation');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].category).toBe('network');
    });

    it('should handle function errors', () => {
      const originalFn = jest.fn(() => {
        throw new Error('Test error');
      });
      const wrappedFn = withPerformanceMonitoring(originalFn, 'error-operation', 'computation');

      expect(() => wrappedFn()).toThrow('Test error');

      const metrics = performanceMonitor.getMetricsByOperation('error-operation_error');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].metadata?.error).toBe('Test error');
    });

    it('should handle async function errors', async () => {
      const originalFn = jest.fn(async () => {
        throw new Error('Async error');
      });
      const wrappedFn = withPerformanceMonitoring(originalFn, 'async-error-operation', 'network');

      await expect(wrappedFn()).rejects.toThrow('Async error');

      const metrics = performanceMonitor.getMetricsByOperation('async-error-operation_error');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].metadata?.error).toBe('Async error');
    });

    it('should include metadata', () => {
      const originalFn = jest.fn(() => 'result');
      const metadata = { userId: '123' };
      const wrappedFn = withPerformanceMonitoring(originalFn, 'test-operation', 'computation', metadata);

      wrappedFn('arg1', 'arg2');

      const metrics = performanceMonitor.getMetricsByOperation('test-operation');
      expect(metrics[0].metadata).toEqual({
        userId: '123',
        args: 2,
      });
    });
  });

  describe('performanceMonitored decorator', () => {
    it('should decorate class methods', () => {
      class TestClass {
        @performanceMonitored('testMethod', 'computation')
        testMethod(x: number): number {
          return x * 2;
        }
      }

      mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1100);

      const instance = new TestClass();
      const result = instance.testMethod(5);

      expect(result).toBe(10);

      const metrics = performanceMonitor.getMetricsByOperation('TestClass.testMethod');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].duration).toBe(100);
      expect(metrics[0].category).toBe('computation');
    });
  });

  describe('measurePerformance', () => {
    beforeEach(() => {
      mockPerformanceNow.mockReturnValueOnce(1000).mockReturnValueOnce(1200);
    });

    it('should measure synchronous operations', async () => {
      const operation = jest.fn(() => 'sync result');
      const metadata = { type: 'sync' };

      const result = await measurePerformance('sync-operation', operation, 'computation', metadata);

      expect(result).toBe('sync result');
      expect(operation).toHaveBeenCalled();

      const metrics = performanceMonitor.getMetricsByOperation('sync-operation');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].duration).toBe(200);
      expect(metrics[0].metadata).toEqual(metadata);
    });

    it('should measure asynchronous operations', async () => {
      const operation = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'async result';
      });

      const result = await measurePerformance('async-operation', operation, 'network');

      expect(result).toBe('async result');
      expect(operation).toHaveBeenCalled();

      const metrics = performanceMonitor.getMetricsByOperation('async-operation');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].category).toBe('network');
    });

    it('should handle operation errors', async () => {
      const operation = jest.fn(async () => {
        throw new Error('Operation failed');
      });

      await expect(measurePerformance('failing-operation', operation)).rejects.toThrow('Operation failed');

      const metrics = performanceMonitor.getMetricsByOperation('failing-operation_error');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].metadata?.error).toBe('Operation failed');
    });
  });

  describe('startMemoryMonitoring', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should start periodic memory monitoring', () => {
      const stopMonitoring = startMemoryMonitoring(5000);

      expect(console.log).toHaveBeenCalledWith('Memory monitoring started (interval: 5000ms)');

      // Fast-forward time to trigger memory recording
      jest.advanceTimersByTime(5000);

      const report = performanceMonitor.getReport();
      expect(report.memoryUsage.length).toBeGreaterThan(0);

      stopMonitoring();
      expect(console.log).toHaveBeenCalledWith('Memory monitoring stopped');
    });

    it('should use default interval when not specified', () => {
      const stopMonitoring = startMemoryMonitoring();

      expect(console.log).toHaveBeenCalledWith('Memory monitoring started (interval: 30000ms)');

      stopMonitoring();
    });
  });

  describe('usePerformanceMonitoring', () => {
    beforeEach(() => {
      mockPerformanceNow.mockReturnValue(1000);
    });

    it('should record component render performance', () => {
      const TestComponent = () => {
        usePerformanceMonitoring('TestComponent');
        return React.createElement('div', null, 'Test');
      };

      renderHook(() => usePerformanceMonitoring('TestComponent'));

      // The effect should run and record a metric
      const metrics = performanceMonitor.getMetricsByOperation('TestComponent_render');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].category).toBe('ui');
    });
  });

  describe('usePerformanceReport', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      // Add some test data
      performanceMonitor.recordMetric({
        operation: 'test-operation',
        duration: 100,
        category: 'computation',
      });
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should provide performance report and refresh function', () => {
      const { result } = renderHook(() => usePerformanceReport());

      expect(result.current.report).toBeDefined();
      expect(result.current.report?.totalOperations).toBe(1);
      expect(typeof result.current.refreshReport).toBe('function');
    });

    it('should auto-refresh report every 10 seconds', () => {
      const { result } = renderHook(() => usePerformanceReport());

      const initialReport = result.current.report;

      // Add more metrics
      act(() => {
        performanceMonitor.recordMetric({
          operation: 'new-operation',
          duration: 50,
          category: 'timer',
        });
      });

      // Fast-forward time to trigger auto-refresh
      act(() => {
        jest.advanceTimersByTime(10000);
      });

      expect(result.current.report?.totalOperations).toBe(2);
    });

    it('should manually refresh report', () => {
      const { result } = renderHook(() => usePerformanceReport());

      expect(result.current.report?.totalOperations).toBe(1);

      // Add more metrics
      act(() => {
        performanceMonitor.recordMetric({
          operation: 'manual-refresh-test',
          duration: 75,
          category: 'storage',
        });
      });

      // Manually refresh
      act(() => {
        result.current.refreshReport();
      });

      expect(result.current.report?.totalOperations).toBe(2);
    });
  });

  describe('recommendations generation', () => {
    beforeEach(() => {
      performanceMonitor.clearMetrics();
    });

    it('should recommend optimization for frequent slow operations', () => {
      // Add many slow operations (>10% of total)
      for (let i = 0; i < 5; i++) {
        performanceMonitor.recordMetric({
          operation: `slow-op-${i}`,
          duration: 150,
          category: 'computation',
        });
      }
      // Add few fast operations
      for (let i = 0; i < 3; i++) {
        performanceMonitor.recordMetric({
          operation: `fast-op-${i}`,
          duration: 50,
          category: 'computation',
        });
      }

      const report = performanceMonitor.getReport();
      expect(report.recommendations).toContain('Consider optimizing frequently slow operations');
    });

    it('should recommend timer optimization for slow timer operations', () => {
      performanceMonitor.recordMetric({
        operation: 'timer-update',
        duration: 150,
        category: 'timer',
      });

      const report = performanceMonitor.getReport();
      expect(report.recommendations).toContain('Timer operations are slow - check system tray updates');
    });

    it('should recommend storage optimization for slow storage operations', () => {
      performanceMonitor.recordMetric({
        operation: 'data-save',
        duration: 200,
        category: 'storage',
      });

      const report = performanceMonitor.getReport();
      expect(report.recommendations).toContain('Storage operations are slow - consider data optimization');
    });

    it('should recommend memory cleanup for high memory usage', () => {
      // Mock high memory usage
      Object.defineProperty(global.performance, 'memory', {
        value: {
          usedJSHeapSize: 85 * 1024 * 1024, // 85MB
          totalJSHeapSize: 90 * 1024 * 1024,
          jsHeapSizeLimit: 100 * 1024 * 1024,
        },
        writable: true,
      });

      performanceMonitor.recordMemoryUsage();
      const report = performanceMonitor.getReport();
      expect(report.recommendations).toContain('High memory usage detected - consider implementing data cleanup');
    });

    it('should provide positive feedback when performance is good', () => {
      performanceMonitor.recordMetric({
        operation: 'fast-operation',
        duration: 50,
        category: 'computation',
      });

      const report = performanceMonitor.getReport();
      expect(report.recommendations).toContain('Performance looks good! No issues detected.');
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle missing performance.memory gracefully', () => {
      delete (global.performance as any).memory;

      expect(() => performanceMonitor.recordMemoryUsage()).not.toThrow();

      const report = performanceMonitor.getReport();
      expect(report.memoryUsage).toHaveLength(0);
    });

    it('should handle non-Error objects in catch blocks', () => {
      const originalFn = jest.fn(() => {
        throw 'String error';
      });
      const wrappedFn = withPerformanceMonitoring(originalFn, 'string-error-operation');

      expect(() => wrappedFn()).toThrow('String error');

      const metrics = performanceMonitor.getMetricsByOperation('string-error-operation_error');
      expect(metrics[0].metadata?.error).toBe('Unknown error');
    });

    it('should handle very large numbers of metrics', () => {
      // Test with exactly the limit
      for (let i = 0; i < 1000; i++) {
        performanceMonitor.recordMetric({
          operation: `operation-${i}`,
          duration: 50,
          category: 'computation',
        });
      }

      const report = performanceMonitor.getReport();
      expect(report.totalOperations).toBe(1000);
      expect(report.averageDuration).toBe(50);
    });

    it('should handle zero duration operations', () => {
      performanceMonitor.recordMetric({
        operation: 'instant-operation',
        duration: 0,
        category: 'computation',
      });

      const report = performanceMonitor.getReport();
      expect(report.totalOperations).toBe(1);
      expect(report.averageDuration).toBe(0);
    });

    it('should handle negative duration operations', () => {
      performanceMonitor.recordMetric({
        operation: 'negative-duration',
        duration: -10,
        category: 'computation',
      });

      const metrics = performanceMonitor.getMetricsByOperation('negative-duration');
      expect(metrics[0].severity).toBe('normal'); // Negative duration treated as normal
    });
  });
});
